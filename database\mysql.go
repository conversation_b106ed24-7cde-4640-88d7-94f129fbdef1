package database

import (
	"fmt"
	"log"
	"time"

	"goHomework/config"
	"goHomework/model"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func NewMySQLConnection(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL: %v", err)
	}

	// 自动迁移表结构
	err = db.AutoMigrate(&model.Customer{})
	if err != nil {
		// 如果迁移失败，先删除可能存在的冲突表
		err = db.Exec("DROP TABLE IF EXISTS user").Error
		if err != nil {
			return nil, fmt.Errorf("failed to drop existing table: %v", err)
		}
		// 重新迁移
		err = db.AutoMigrate(&model.Customer{})
		if err != nil {
			return nil, fmt.Errorf("failed to migrate database: %v", err)
		}
	}

	// 初始化种子数据
	err = seedData(db)
	if err != nil {
		log.Printf("⚠️ 种子数据初始化失败: %v", err)
	}

	log.Printf("✅ 成功连接到 MySQL 数据库: %s", cfg.Database)
	return db, nil
}

// seedData 初始化种子数据
func seedData(db *gorm.DB) error {
	// 检查是否已有用户数据
	var count int64
	db.Model(&model.Customer{}).Count(&count)
	if count > 0 {
		log.Println("数据库已有用户数据，跳过种子数据初始化")
		return nil
	}

	log.Println("正在初始化种子数据...")

	// 创建测试用户
	testUsers := []struct {
		Username    string
		Name        string
		Gender      string
		Age         int
		Phone       string
		Email       string
		Password    string
		Address     string
		Description string
	}{
		{
			Username:    "zhangsan",
			Name:        "张三",
			Gender:      "男",
			Age:         25,
			Phone:       "13800138001",
			Email:       "<EMAIL>",
			Password:    "123456",
			Address:     "北京市朝阳区",
			Description: "测试用户1",
		},
		{
			Username:    "lisi",
			Name:        "李四",
			Gender:      "女",
			Age:         30,
			Phone:       "13800138002",
			Email:       "<EMAIL>",
			Password:    "123456",
			Address:     "上海市浦东新区",
			Description: "测试用户2",
		},
		{
			Username:    "wangwu",
			Name:        "王五",
			Gender:      "男",
			Age:         28,
			Phone:       "13800138003",
			Email:       "<EMAIL>",
			Password:    "123456",
			Address:     "广州市天河区",
			Description: "测试用户3",
		},
	}

	now := time.Now()
	for _, user := range testUsers {
		// 加密密码
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
		if err != nil {
			log.Printf("密码加密失败: %v", err)
			continue
		}

		customer := model.Customer{
			Username:    user.Username,
			Name:        user.Name,
			Gender:      user.Gender,
			Age:         user.Age,
			Phone:       user.Phone,
			Email:       user.Email,
			Password:    string(hashedPassword),
			Status:      model.UserStatusActive,
			Address:     user.Address,
			Description: user.Description,
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		result := db.Create(&customer)
		if result.Error != nil {
			log.Printf("创建用户 %s 失败: %v", user.Name, result.Error)
		} else {
			log.Printf("✅ 创建测试用户: %s", user.Name)
		}
	}

	log.Println("✅ 种子数据初始化完成")
	return nil
}
