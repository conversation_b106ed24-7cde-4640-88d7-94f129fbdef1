package service

import (
	"errors"
	"goHomework/model"
	"math"
	"regexp"
	"sync"

	"gorm.io/gorm"
)

type CustomerService struct {
	customers []model.Customer
	nextID    int
	mutex     sync.RWMutex
	db        *gorm.DB
}

func NewCustomerService() *CustomerService {
	return &CustomerService{
		customers: []model.Customer{},
		nextID:    1,
	}
}

// NewCustomerServiceWithDB creates a new CustomerService with database connection
func NewCustomerServiceWithDB(db *gorm.DB) *CustomerService {
	return &CustomerService{
		db:     db,
		nextID: 1, // This will be managed by the database auto-increment
	}
}

// GetAllCustomers 获取所有客户（已废弃，请使用 GetCustomerList）
func (s *CustomerService) GetAllCustomers() []model.Customer {
	if s.db != nil {
		// 使用数据库
		var customers []model.Customer
		s.db.Where("status != ?", model.UserStatusDeleted).Find(&customers)
		return customers
	}
	return []model.Customer{}
}

// GetCustomerList 获取客户列表（支持分页和搜索）
func (s *CustomerService) GetCustomerList(req model.CustomerListRequest) (*model.CustomerListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	if s.db != nil {
		// 使用数据库
		var customers []model.Customer
		var total int64

		query := s.db.Model(&model.Customer{})

		// 状态过滤
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		} else {
			query = query.Where("status != ?", model.UserStatusDeleted)
		}

		// 搜索过滤（用户名、邮箱、电话、昵称）
		if req.Search != "" {
			searchPattern := "%" + req.Search + "%"
			query = query.Where("username LIKE ? OR email LIKE ? OR phone LIKE ? OR nickname LIKE ?",
				searchPattern, searchPattern, searchPattern, searchPattern)
		}

		// 获取总数
		query.Count(&total)

		// 分页查询
		offset := (req.Page - 1) * req.PageSize
		query.Offset(offset).Limit(req.PageSize).Find(&customers)

		totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

		return &model.CustomerListResponse{
			Data:       customers,
			Total:      total,
			Page:       req.Page,
			PageSize:   req.PageSize,
			TotalPages: totalPages,
		}, nil
	}

	return &model.CustomerListResponse{
		Data:       []model.Customer{},
		Total:      0,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: 0,
	}, nil
}

func (s *CustomerService) GetCustomerByID(id int) (*model.Customer, error) {
	if s.db != nil {
		// 使用数据库
		var customer model.Customer
		result := s.db.Where("user_id = ? AND status != ?", id, model.UserStatusDeleted).First(&customer)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return nil, errors.New("customer not found")
			}
			return nil, result.Error
		}
		return &customer, nil
	}
	return nil, errors.New("customer not found")
}

// CreateCustomer 创建用户（已废弃，请使用认证服务的注册功能）
func (s *CustomerService) CreateCustomer() error {
	return errors.New("请使用认证服务的注册功能创建用户")
}

func (s *CustomerService) UpdateCustomer(id int, req model.UpdateCustomerRequest) (*model.Customer, error) {
	if s.db != nil {
		// 使用数据库
		var customer model.Customer
		result := s.db.Where("user_id = ? AND status != ?", id, model.UserStatusDeleted).First(&customer)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return nil, errors.New("customer not found")
			}
			return nil, result.Error
		}

		// 更新字段
		updates := map[string]interface{}{}
		if req.Phone != nil {
			updates["phone"] = *req.Phone
		}
		if req.Email != nil {
			updates["email"] = *req.Email
		}
		if req.Avatar != nil {
			updates["avatar"] = *req.Avatar
		}
		if req.Address != nil {
			updates["address"] = *req.Address
		}
		if req.Nickname != nil {
			updates["nickname"] = *req.Nickname
		}

		if len(updates) > 0 {
			result = s.db.Model(&customer).Updates(updates)
			if result.Error != nil {
				return nil, result.Error
			}
		}

		// 重新查询更新后的数据
		s.db.Where("user_id = ?", id).First(&customer)
		return &customer, nil
	}

	return nil, errors.New("customer not found")
}

func (s *CustomerService) DeleteCustomer(id int) error {
	if s.db != nil {
		// 使用数据库
		result := s.db.Model(&model.Customer{}).
			Where("user_id = ? AND status != ?", id, model.UserStatusDeleted).
			Update("status", model.UserStatusDeleted)
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return errors.New("customer not found")
		}
		return nil
	}

	return errors.New("customer not found")
}

// UpdateCustomerStatus 更新用户状态
func (s *CustomerService) UpdateCustomerStatus(id int, status int) error {
	if s.db != nil {
		// 使用数据库
		result := s.db.Model(&model.Customer{}).
			Where("user_id = ? AND status != ?", id, model.UserStatusDeleted).
			Update("status", status)
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return errors.New("customer not found")
		}
		return nil
	}
	return errors.New("customer not found")
}

// BatchDeleteCustomers 批量删除用户
func (s *CustomerService) BatchDeleteCustomers(ids []int) error {
	if s.db != nil {
		// 使用数据库
		result := s.db.Model(&model.Customer{}).
			Where("user_id IN ? AND status != ?", ids, model.UserStatusDeleted).
			Update("status", model.UserStatusDeleted)
		return result.Error
	}
	return nil
}

// GetCustomerStats 获取用户统计信息
func (s *CustomerService) GetCustomerStats() map[string]interface{} {
	stats := map[string]interface{}{
		"total":    0,
		"active":   0,
		"inactive": 0,
		"deleted":  0,
	}

	if s.db != nil {
		// 使用数据库
		var total int64
		s.db.Model(&model.Customer{}).Count(&total)
		stats["total"] = int(total)

		var active int64
		s.db.Model(&model.Customer{}).Where("status = ?", model.UserStatusActive).Count(&active)
		stats["active"] = int(active)

		var inactive int64
		s.db.Model(&model.Customer{}).Where("status = ?", model.UserStatusInactive).Count(&inactive)
		stats["inactive"] = int(inactive)

		// 删除 blocked 状态，因为新的数据库结构中没有这个状态

		var deleted int64
		s.db.Model(&model.Customer{}).Where("status = ?", model.UserStatusDeleted).Count(&deleted)
		stats["deleted"] = int(deleted)

		return stats
	}

	return stats
}

// validateUniqueFields 验证邮箱和手机号的唯一性
func (s *CustomerService) validateUniqueFields(email, phone string, excludeID int) error {
	if s.db != nil {
		// 使用数据库
		if email != "" {
			var count int64
			query := s.db.Model(&model.Customer{}).Where("email = ? AND status != ?", email, model.UserStatusDeleted)
			if excludeID > 0 {
				query = query.Where("user_id != ?", excludeID)
			}
			query.Count(&count)
			if count > 0 {
				return errors.New("邮箱已存在")
			}
		}

		if phone != "" {
			var count int64
			query := s.db.Model(&model.Customer{}).Where("phone = ? AND status != ?", phone, model.UserStatusDeleted)
			if excludeID > 0 {
				query = query.Where("user_id != ?", excludeID)
			}
			query.Count(&count)
			if count > 0 {
				return errors.New("手机号已存在")
			}
		}
		return nil
	}

	return nil
}

// validateEmail 验证邮箱格式
func (s *CustomerService) validateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return errors.New("邮箱格式不正确")
	}
	return nil
}

// validatePhone 验证手机号格式
func (s *CustomerService) validatePhone(phone string) error {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		return errors.New("手机号格式不正确")
	}
	return nil
}

// SearchCustomers 搜索用户（模糊搜索）
func (s *CustomerService) SearchCustomers(keyword string) []model.Customer {
	if s.db != nil {
		// 使用数据库
		var customers []model.Customer
		searchPattern := "%" + keyword + "%"
		s.db.Where("status != ? AND (username LIKE ? OR email LIKE ? OR phone LIKE ? OR address LIKE ? OR nickname LIKE ?)",
			model.UserStatusDeleted, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern).
			Find(&customers)
		return customers
	}
	return []model.Customer{}
}
