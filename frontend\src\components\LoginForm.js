import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Mail, Lock, LogIn } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const LoginForm = ({ onSwitchToRegister }) => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, loading } = useAuth();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm();

  const onSubmit = async (data) => {
    try {
      const result = await login(data);
      if (!result.success) {
        setError('root', {
          type: 'manual',
          message: result.message || '登录失败'
        });
      }
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: '登录过程中发生错误'
      });
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="card p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">欢迎回来</h2>
          <p className="text-gray-600">请登录您的账户</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 用户名输入 */}
          <div className="form-group">
            <label className="form-label">
              <Mail className="w-4 h-4 inline mr-2" />
              用户名
            </label>
            <input
              type="text"
              className={`form-input ${errors.username ? 'error' : ''}`}
              placeholder="请输入用户名"
              {...register('username', {
                required: '用户名不能为空',
                minLength: {
                  value: 2,
                  message: '用户名至少2个字符'
                },
                maxLength: {
                  value: 50,
                  message: '用户名最多50个字符'
                }
              })}
            />
            {errors.username && (
              <p className="form-error">{errors.username.message}</p>
            )}
          </div>

          {/* 密码输入 */}
          <div className="form-group">
            <label className="form-label">
              <Lock className="w-4 h-4 inline mr-2" />
              密码
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className={`form-input pr-12 ${errors.password ? 'error' : ''}`}
                placeholder="请输入密码"
                {...register('password', {
                  required: '密码不能为空',
                  minLength: {
                    value: 6,
                    message: '密码至少需要6个字符'
                  }
                })}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )}
          </div>

          {/* 记住我和忘记密码 */}
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                {...register('rememberMe')}
              />
              <span className="ml-2 text-sm text-gray-600">记住我</span>
            </label>
            <button
              type="button"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              忘记密码？
            </button>
          </div>

          {/* 错误信息 */}
          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{errors.root.message}</p>
            </div>
          )}

          {/* 登录按钮 */}
          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary w-full"
          >
            {loading ? (
              <>
                <div className="loading"></div>
                登录中...
              </>
            ) : (
              <>
                <LogIn className="w-4 h-4" />
                登录
              </>
            )}
          </button>
        </form>

        {/* 切换到注册 */}
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            还没有账户？
            <button
              type="button"
              onClick={onSwitchToRegister}
              className="text-blue-600 hover:text-blue-800 font-medium ml-1"
            >
              立即注册
            </button>
          </p>
        </div>

        {/* 演示账户信息 */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-2">演示账户</h4>
          <div className="text-xs text-blue-600 space-y-1">
            <p>用户名: admin / 密码: 123456</p>
            <p>用户名: asus_manager / 密码: 123456</p>
            <p>用户名: msi_manager / 密码: 123456</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
