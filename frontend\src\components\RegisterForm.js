import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, User, Mail, Lock, Phone, MapPin, UserPlus } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const RegisterForm = ({ onSwitchToLogin }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { register: registerUser, loading } = useAuth();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setError
  } = useForm();

  const password = watch('password');

  const onSubmit = async (data) => {
    try {
      // 移除确认密码字段
      const { confirmPassword, ...userData } = data;
      
      const result = await registerUser(userData);
      if (result.success) {
        // 注册成功，切换到登录页面
        onSwitchToLogin();
      } else {
        setError('root', {
          type: 'manual',
          message: result.message || '注册失败'
        });
      }
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: '注册过程中发生错误'
      });
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="card p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <UserPlus className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">创建账户</h2>
          <p className="text-gray-600">请填写以下信息完成注册</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 用户名输入 */}
          <div className="form-group">
            <label className="form-label">
              <User className="w-4 h-4 inline mr-2" />
              用户名
            </label>
            <input
              type="text"
              className={`form-input ${errors.username ? 'error' : ''}`}
              placeholder="请输入用户名"
              {...register('username', {
                required: '用户名不能为空',
                minLength: {
                  value: 2,
                  message: '用户名至少需要2个字符'
                },
                maxLength: {
                  value: 50,
                  message: '用户名不能超过50个字符'
                },
                pattern: {
                  value: /^[a-zA-Z0-9_]+$/,
                  message: '用户名只能包含字母、数字和下划线'
                }
              })}
            />
            {errors.username && (
              <p className="form-error">{errors.username.message}</p>
            )}
          </div>

          {/* 姓名输入 */}
          <div className="form-group">
            <label className="form-label">
              <User className="w-4 h-4 inline mr-2" />
              姓名
            </label>
            <input
              type="text"
              className={`form-input ${errors.name ? 'error' : ''}`}
              placeholder="请输入您的姓名"
              {...register('name', {
                required: '姓名不能为空',
                minLength: {
                  value: 2,
                  message: '姓名至少需要2个字符'
                },
                maxLength: {
                  value: 50,
                  message: '姓名不能超过50个字符'
                }
              })}
            />
            {errors.name && (
              <p className="form-error">{errors.name.message}</p>
            )}
          </div>

          {/* 邮箱输入 */}
          <div className="form-group">
            <label className="form-label">
              <Mail className="w-4 h-4 inline mr-2" />
              邮箱地址
            </label>
            <input
              type="email"
              className={`form-input ${errors.email ? 'error' : ''}`}
              placeholder="请输入邮箱地址"
              {...register('email', {
                required: '邮箱地址不能为空',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: '请输入有效的邮箱地址'
                }
              })}
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>

          {/* 手机号输入 */}
          <div className="form-group">
            <label className="form-label">
              <Phone className="w-4 h-4 inline mr-2" />
              手机号码
            </label>
            <input
              type="tel"
              className={`form-input ${errors.phone ? 'error' : ''}`}
              placeholder="请输入手机号码"
              {...register('phone', {
                required: '手机号码不能为空',
                pattern: {
                  value: /^1[3-9]\d{9}$/,
                  message: '请输入有效的手机号码'
                }
              })}
            />
            {errors.phone && (
              <p className="form-error">{errors.phone.message}</p>
            )}
          </div>

          {/* 性别和年龄 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">性别</label>
              <select
                className={`form-input ${errors.gender ? 'error' : ''}`}
                {...register('gender', {
                  required: '请选择性别'
                })}
              >
                <option value="">请选择</option>
                <option value="男">男</option>
                <option value="女">女</option>
                <option value="其他">其他</option>
              </select>
              {errors.gender && (
                <p className="form-error">{errors.gender.message}</p>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">年龄</label>
              <input
                type="number"
                className={`form-input ${errors.age ? 'error' : ''}`}
                placeholder="年龄"
                {...register('age', {
                  required: '年龄不能为空',
                  min: {
                    value: 1,
                    message: '年龄必须大于0'
                  },
                  max: {
                    value: 150,
                    message: '年龄不能超过150'
                  }
                })}
              />
              {errors.age && (
                <p className="form-error">{errors.age.message}</p>
              )}
            </div>
          </div>

          {/* 地址输入 */}
          <div className="form-group">
            <label className="form-label">
              <MapPin className="w-4 h-4 inline mr-2" />
              地址 <span className="text-gray-400">(可选)</span>
            </label>
            <input
              type="text"
              className="form-input"
              placeholder="请输入您的地址"
              {...register('address')}
            />
          </div>

          {/* 密码输入 */}
          <div className="form-group">
            <label className="form-label">
              <Lock className="w-4 h-4 inline mr-2" />
              密码
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className={`form-input pr-12 ${errors.password ? 'error' : ''}`}
                placeholder="请输入密码"
                {...register('password', {
                  required: '密码不能为空',
                  minLength: {
                    value: 6,
                    message: '密码至少需要6个字符'
                  }
                })}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )}
          </div>

          {/* 确认密码输入 */}
          <div className="form-group">
            <label className="form-label">
              <Lock className="w-4 h-4 inline mr-2" />
              确认密码
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                className={`form-input pr-12 ${errors.confirmPassword ? 'error' : ''}`}
                placeholder="请再次输入密码"
                {...register('confirmPassword', {
                  required: '请确认密码',
                  validate: value =>
                    value === password || '两次输入的密码不一致'
                })}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="form-error">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* 错误信息 */}
          {errors.root && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{errors.root.message}</p>
            </div>
          )}

          {/* 注册按钮 */}
          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary w-full"
          >
            {loading ? (
              <>
                <div className="loading"></div>
                注册中...
              </>
            ) : (
              <>
                <UserPlus className="w-4 h-4" />
                注册
              </>
            )}
          </button>
        </form>

        {/* 切换到登录 */}
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            已有账户？
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="text-blue-600 hover:text-blue-800 font-medium ml-1"
            >
              立即登录
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegisterForm;
