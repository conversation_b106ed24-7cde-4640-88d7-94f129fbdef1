# 用户管理服务器系统

一个基于 Go + Gin 框架的现代化用户管理服务器系统，提供完整的 RESTful API 接口。

## 功能特性

### 核心功能
- ✅ 用户 CRUD 操作（创建、读取、更新、删除）
- ✅ 用户状态管理（活跃、非活跃、封禁、删除）
- ✅ 分页查询和搜索功能
- ✅ 批量操作支持
- ✅ 数据验证和错误处理

### 系统特性
- ✅ 统一的 API 响应格式
- ✅ 请求日志和错误恢复中间件
- ✅ 速率限制保护
- ✅ CORS 跨域支持
- ✅ 优雅关闭
- ✅ 配置管理（环境变量）
- ✅ 健康检查和指标监控

## 项目结构

```
├── config/          # 配置管理
├── handler/         # HTTP 处理器
├── middleware/      # 中间件
├── model/          # 数据模型和响应结构
├── service/        # 业务逻辑层
├── main.go         # 应用入口
├── .env.example    # 环境变量示例
└── README.md       # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

根据需要修改 `.env` 文件中的配置。

### 3. 启动服务器

```bash
go run main.go
```

服务器将在 `http://localhost:8080` 启动。

## API 接口

### 基础接口

- `GET /health` - 健康检查
- `GET /metrics` - 系统指标

### 用户管理接口

#### 获取用户列表（支持分页和搜索）
```http
GET /api/v1/customers?page=1&page_size=10&search=张三&status=active&gender=男
```

#### 获取所有用户（兼容旧版本）
```http
GET /api/v1/customers/all
```

#### 搜索用户
```http
GET /api/v1/customers/search?keyword=张三
```

#### 获取用户统计信息
```http
GET /api/v1/customers/stats
```

#### 获取单个用户
```http
GET /api/v1/customers/{id}
```

#### 创建用户
```http
POST /api/v1/customers
Content-Type: application/json

{
  "name": "张三",
  "gender": "男",
  "age": 25,
  "phone": "13800138001",
  "email": "<EMAIL>",
  "address": "北京市朝阳区",
  "description": "测试用户"
}
```

#### 更新用户
```http
PUT /api/v1/customers/{id}
Content-Type: application/json

{
  "name": "张三三",
  "age": 26
}
```

#### 更新用户状态
```http
PATCH /api/v1/customers/{id}/status
Content-Type: application/json

{
  "status": "inactive"
}
```

#### 删除用户
```http
DELETE /api/v1/customers/{id}
```

#### 批量删除用户
```http
DELETE /api/v1/customers/batch
Content-Type: application/json

{
  "ids": [1, 2, 3]
}
```

## 响应格式

所有 API 响应都遵循统一格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01 12:00:00",
  "request_id": "abc123"
}
```

错误响应：
```json
{
  "success": false,
  "message": "错误信息",
  "error": {
    "code": "ERROR_CODE",
    "message": "详细错误信息",
    "details": {}
  },
  "timestamp": "2024-01-01 12:00:00",
  "request_id": "abc123"
}
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SERVER_HOST` | `0.0.0.0` | 服务器监听地址 |
| `SERVER_PORT` | `8080` | 服务器端口 |
| `GIN_MODE` | `debug` | Gin 运行模式 |
| `RATE_LIMIT_ENABLED` | `true` | 是否启用速率限制 |
| `RATE_LIMIT_RATE` | `100` | 每分钟允许请求数 |
| `LOG_LEVEL` | `info` | 日志级别 |

更多配置选项请参考 `.env.example` 文件。

## 开发指南

### 添加新的 API 接口

1. 在 `model/` 中定义请求和响应结构
2. 在 `service/` 中实现业务逻辑
3. 在 `handler/` 中添加 HTTP 处理器
4. 在 `main.go` 中注册路由

### 自定义中间件

在 `middleware/` 目录下创建新的中间件文件，然后在 `main.go` 中注册使用。

## 部署

### Docker 部署

创建 `Dockerfile`：
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

构建和运行：
```bash
docker build -t user-management .
docker run -p 8080:8080 user-management
```

## 前端应用

### React 登录注册系统

我们还为您创建了一个完整的 React 前端应用，提供现代化的用户界面：

#### 功能特性
- 🔐 用户登录和注册
- 📱 响应式设计（支持移动端）
- 🎨 现代化 UI 界面
- ✅ 表单验证和错误处理
- 🔄 自动登录状态保持
- 🛡️ 路由保护

#### 快速启动前端

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start
```

前端应用将在 `http://localhost:3000` 启动。

#### 演示账户
- 用户名：张三 / 密码：123456
- 用户名：李四 / 密码：123456
- 用户名：王五 / 密码：123456

### 一键启动脚本

为了方便开发，我们提供了一键启动脚本：

**Windows:**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/macOS:**
```bash
# 添加执行权限
chmod +x start.sh

# 运行脚本
./start.sh
```

这个脚本会自动：
1. 检查环境依赖
2. 启动后端服务（端口 8080）
3. 安装前端依赖（如果需要）
4. 启动前端服务（端口 3000）

## 完整的技术栈

### 后端
- **Go 1.21+** - 编程语言
- **Gin** - Web 框架
- **内存存储** - 数据存储（可扩展为数据库）

### 前端
- **React 18** - 前端框架
- **React Router v6** - 路由管理
- **React Hook Form** - 表单处理
- **Axios** - HTTP 客户端
- **React Hot Toast** - 消息提示

### 系统特性
- **RESTful API** - 标准化接口设计
- **CORS 支持** - 跨域资源共享
- **请求日志** - 详细的请求记录
- **错误处理** - 统一的错误响应
- **速率限制** - 防止恶意请求
- **优雅关闭** - 安全的服务停止

## 项目结构

```
gohomeworkproject/
├── backend/                 # 后端 Go 服务
│   ├── config/             # 配置管理
│   ├── handler/            # HTTP 处理器
│   ├── middleware/         # 中间件
│   ├── model/             # 数据模型
│   ├── service/           # 业务逻辑
│   └── main.go            # 应用入口
├── frontend/               # 前端 React 应用
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   │   ├── components/    # React 组件
│   │   ├── contexts/      # Context 状态管理
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API 服务
│   │   └── utils/         # 工具函数
│   └── package.json       # 前端依赖
├── start.bat              # Windows 启动脚本
├── start.sh               # Linux/macOS 启动脚本
└── README.md              # 项目说明
```

## 开发指南

### 添加新功能

1. **后端 API**：
   - 在 `model/` 中定义数据结构
   - 在 `service/` 中实现业务逻辑
   - 在 `handler/` 中添加 HTTP 处理器
   - 在 `main.go` 中注册路由

2. **前端页面**：
   - 在 `src/pages/` 中创建页面组件
   - 在 `src/services/api.js` 中添加 API 调用
   - 在 `src/App.js` 中配置路由

### 环境配置

**后端环境变量：**
```env
SERVER_PORT=8080
GIN_MODE=debug
RATE_LIMIT_ENABLED=true
LOG_LEVEL=info
```

**前端环境变量：**
```env
REACT_APP_API_URL=http://localhost:8080
REACT_APP_TITLE=用户管理系统
```

## 部署指南

### Docker 部署

1. **后端 Dockerfile**：
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

2. **前端 Dockerfile**：
```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 生产环境

1. 设置环境变量为生产模式
2. 配置反向代理（Nginx）
3. 启用 HTTPS
4. 配置数据库（替换内存存储）
5. 设置监控和日志

## 许可证

MIT License
