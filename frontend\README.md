# 用户管理系统 - 前端

一个基于 React 的现代化用户管理系统前端应用，提供登录注册和用户管理功能。

## 功能特性

### 🔐 认证功能
- ✅ 用户登录
- ✅ 用户注册
- ✅ 自动登录状态保持
- ✅ 安全退出
- ✅ 路由保护

### 🎨 界面特性
- ✅ 现代化 UI 设计
- ✅ 响应式布局（支持移动端）
- ✅ 流畅的动画效果
- ✅ 友好的错误提示
- ✅ 加载状态指示

### 📱 用户体验
- ✅ 表单验证
- ✅ 实时错误提示
- ✅ 密码可见性切换
- ✅ 记住登录状态
- ✅ 演示账户信息

## 技术栈

- **React 18** - 前端框架
- **React Router v6** - 路由管理
- **React Hook Form** - 表单处理
- **Axios** - HTTP 客户端
- **React Hot Toast** - 消息提示
- **Lucide React** - 图标库
- **CSS3** - 样式和动画

## 项目结构

```
frontend/
├── public/
│   └── index.html          # HTML 模板
├── src/
│   ├── components/         # 可复用组件
│   │   ├── LoginForm.js    # 登录表单
│   │   ├── RegisterForm.js # 注册表单
│   │   └── ProtectedRoute.js # 路由保护
│   ├── contexts/          # React Context
│   │   └── AuthContext.js # 认证状态管理
│   ├── pages/             # 页面组件
│   │   ├── AuthPage.js    # 认证页面
│   │   └── Dashboard.js   # 用户仪表板
│   ├── services/          # API 服务
│   │   └── api.js         # API 调用封装
│   ├── utils/             # 工具函数
│   │   └── validation.js  # 表单验证
│   ├── App.js             # 主应用组件
│   ├── index.js           # 应用入口
│   └── index.css          # 全局样式
├── package.json           # 项目配置
└── README.md             # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

应用将在 `http://localhost:3000` 启动。

### 3. 构建生产版本

```bash
npm run build
```

## 环境配置

### 环境变量

创建 `.env` 文件配置环境变量：

```env
# API 服务器地址
REACT_APP_API_URL=http://localhost:8080

# 应用标题
REACT_APP_TITLE=用户管理系统
```

### 后端服务

确保后端服务正在运行：
- 后端服务地址：`http://localhost:8080`
- 健康检查：`http://localhost:8080/health`

## 演示账户

系统提供以下演示账户：

| 用户名 | 密码 |
|------|------|
| 张三 | 123456 |
| 李四 | 123456 |
| 王五 | 123456 |

## 主要功能

### 登录页面
- 用户名和密码验证
- 记住登录状态
- 错误提示
- 演示账户信息

### 注册页面
- 完整的用户信息表单
- 实时表单验证
- 密码强度检查
- 重复密码验证

### 用户仪表板
- 个人信息展示
- 系统统计信息
- 快速操作菜单
- 安全退出

## API 集成

### 认证 API
```javascript
// 登录
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 注册
POST /api/v1/customers
{
  "name": "用户姓名",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "gender": "男",
  "age": 25,
  "address": "用户地址"
}
```

### 用户 API
```javascript
// 获取用户统计
GET /api/v1/customers/stats

// 获取用户信息
GET /api/v1/customers/:id
```

## 开发指南

### 添加新页面

1. 在 `src/pages/` 创建新的页面组件
2. 在 `src/App.js` 中添加路由
3. 如需保护路由，使用 `ProtectedRoute` 包装

### 添加新的 API 调用

1. 在 `src/services/api.js` 中添加 API 方法
2. 在组件中导入并使用
3. 处理加载状态和错误

### 自定义样式

1. 在 `src/index.css` 中添加全局样式
2. 使用现有的工具类
3. 遵循响应式设计原则

## 部署

### 构建应用

```bash
npm run build
```

### 部署到静态服务器

将 `build` 目录部署到任何静态文件服务器：

- Nginx
- Apache
- Vercel
- Netlify
- GitHub Pages

### Docker 部署

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 许可证

MIT License
