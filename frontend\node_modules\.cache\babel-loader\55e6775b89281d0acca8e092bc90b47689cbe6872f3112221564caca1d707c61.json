{"ast": null, "code": "var _jsxFileName = \"D:\\\\gohomeworkproject\\\\frontend\\\\src\\\\components\\\\RegisterForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { Eye, EyeOff, User, Mail, Lock, Phone, MapPin, UserPlus } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RegisterForm = ({\n  onSwitchToLogin\n}) => {\n  _s();\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const {\n    register: registerUser,\n    loading\n  } = useAuth();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    watch,\n    setError\n  } = useForm();\n  const password = watch('password');\n  const onSubmit = async data => {\n    try {\n      // 移除确认密码字段\n      const {\n        confirmPassword,\n        ...userData\n      } = data;\n      const result = await registerUser(userData);\n      if (result.success) {\n        // 注册成功，切换到登录页面\n        onSwitchToLogin();\n      } else {\n        setError('root', {\n          type: 'manual',\n          message: result.message || '注册失败'\n        });\n      }\n    } catch (error) {\n      setError('root', {\n        type: 'manual',\n        message: '注册过程中发生错误'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full max-w-md mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(UserPlus, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-800 mb-2\",\n          children: \"\\u521B\\u5EFA\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u8BF7\\u586B\\u5199\\u4EE5\\u4E0B\\u4FE1\\u606F\\u5B8C\\u6210\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), \"\\u7528\\u6237\\u540D\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: `form-input ${errors.username ? 'error' : ''}`,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n            ...register('username', {\n              required: '用户名不能为空',\n              minLength: {\n                value: 2,\n                message: '用户名至少需要2个字符'\n              },\n              maxLength: {\n                value: 50,\n                message: '用户名不能超过50个字符'\n              },\n              pattern: {\n                value: /^[a-zA-Z0-9_]+$/,\n                message: '用户名只能包含字母、数字和下划线'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.username.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), \"\\u59D3\\u540D\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: `form-input ${errors.name ? 'error' : ''}`,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u59D3\\u540D\",\n            ...register('name', {\n              required: '姓名不能为空',\n              minLength: {\n                value: 2,\n                message: '姓名至少需要2个字符'\n              },\n              maxLength: {\n                value: 50,\n                message: '姓名不能超过50个字符'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.name.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(Mail, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), \"\\u90AE\\u7BB1\\u5730\\u5740\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            className: `form-input ${errors.email ? 'error' : ''}`,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n            ...register('email', {\n              required: '邮箱地址不能为空',\n              pattern: {\n                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                message: '请输入有效的邮箱地址'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.email.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(Phone, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), \"\\u624B\\u673A\\u53F7\\u7801\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            className: `form-input ${errors.phone ? 'error' : ''}`,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\\u7801\",\n            ...register('phone', {\n              required: '手机号码不能为空',\n              pattern: {\n                value: /^1[3-9]\\d{9}$/,\n                message: '请输入有效的手机号码'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.phone.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u6027\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: `form-input ${errors.gender ? 'error' : ''}`,\n              ...register('gender', {\n                required: '请选择性别'\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"\\u8BF7\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u7537\",\n                children: \"\\u7537\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u5973\",\n                children: \"\\u5973\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u5176\\u4ED6\",\n                children: \"\\u5176\\u4ED6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), errors.gender && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.gender.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u5E74\\u9F84\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              className: `form-input ${errors.age ? 'error' : ''}`,\n              placeholder: \"\\u5E74\\u9F84\",\n              ...register('age', {\n                required: '年龄不能为空',\n                min: {\n                  value: 1,\n                  message: '年龄必须大于0'\n                },\n                max: {\n                  value: 150,\n                  message: '年龄不能超过150'\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), errors.age && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.age.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(MapPin, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), \"\\u5730\\u5740 \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"(\\u53EF\\u9009)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 18\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-input\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u5730\\u5740\",\n            ...register('address')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), \"\\u5BC6\\u7801\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              className: `form-input pr-12 ${errors.password ? 'error' : ''}`,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n              ...register('password', {\n                required: '密码不能为空',\n                minLength: {\n                  value: 6,\n                  message: '密码至少需要6个字符'\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\",\n              onClick: () => setShowPassword(!showPassword),\n              children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.password.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              className: \"w-4 h-4 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), \"\\u786E\\u8BA4\\u5BC6\\u7801\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: showConfirmPassword ? 'text' : 'password',\n              className: `form-input pr-12 ${errors.confirmPassword ? 'error' : ''}`,\n              placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u5BC6\\u7801\",\n              ...register('confirmPassword', {\n                required: '请确认密码',\n                validate: value => value === password || '两次输入的密码不一致'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\",\n              onClick: () => setShowConfirmPassword(!showConfirmPassword),\n              children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.confirmPassword.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), errors.root && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-600 text-sm\",\n            children: errors.root.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"btn btn-primary w-full\",\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), \"\\u6CE8\\u518C\\u4E2D...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), \"\\u6CE8\\u518C\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"\\u5DF2\\u6709\\u8D26\\u6237\\uFF1F\", /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onSwitchToLogin,\n            className: \"text-blue-600 hover:text-blue-800 font-medium ml-1\",\n            children: \"\\u7ACB\\u5373\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterForm, \"5vY3KtSohI9ise85U/R2EGfxTiU=\", false, function () {\n  return [useAuth, useForm];\n});\n_c = RegisterForm;\nexport default RegisterForm;\nvar _c;\n$RefreshReg$(_c, \"RegisterForm\");", "map": {"version": 3, "names": ["React", "useState", "useForm", "Eye", "Eye<PERSON>ff", "User", "Mail", "Lock", "Phone", "MapPin", "UserPlus", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RegisterForm", "onSwitchToLogin", "_s", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "register", "registerUser", "loading", "handleSubmit", "formState", "errors", "watch", "setError", "password", "onSubmit", "data", "confirmPassword", "userData", "result", "success", "type", "message", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "username", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "value", "max<PERSON><PERSON><PERSON>", "pattern", "name", "email", "phone", "gender", "age", "min", "max", "onClick", "validate", "root", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/gohomeworkproject/frontend/src/components/RegisterForm.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { Eye, EyeOff, User, Mail, Lock, Phone, MapPin, UserPlus } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst RegisterForm = ({ onSwitchToLogin }) => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const { register: registerUser, loading } = useAuth();\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    watch,\n    setError\n  } = useForm();\n\n  const password = watch('password');\n\n  const onSubmit = async (data) => {\n    try {\n      // 移除确认密码字段\n      const { confirmPassword, ...userData } = data;\n      \n      const result = await registerUser(userData);\n      if (result.success) {\n        // 注册成功，切换到登录页面\n        onSwitchToLogin();\n      } else {\n        setError('root', {\n          type: 'manual',\n          message: result.message || '注册失败'\n        });\n      }\n    } catch (error) {\n      setError('root', {\n        type: 'manual',\n        message: '注册过程中发生错误'\n      });\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"card p-8\">\n        <div className=\"text-center mb-8\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <UserPlus className=\"w-8 h-8 text-white\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">创建账户</h2>\n          <p className=\"text-gray-600\">请填写以下信息完成注册</p>\n        </div>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* 用户名输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <User className=\"w-4 h-4 inline mr-2\" />\n              用户名\n            </label>\n            <input\n              type=\"text\"\n              className={`form-input ${errors.username ? 'error' : ''}`}\n              placeholder=\"请输入用户名\"\n              {...register('username', {\n                required: '用户名不能为空',\n                minLength: {\n                  value: 2,\n                  message: '用户名至少需要2个字符'\n                },\n                maxLength: {\n                  value: 50,\n                  message: '用户名不能超过50个字符'\n                },\n                pattern: {\n                  value: /^[a-zA-Z0-9_]+$/,\n                  message: '用户名只能包含字母、数字和下划线'\n                }\n              })}\n            />\n            {errors.username && (\n              <p className=\"form-error\">{errors.username.message}</p>\n            )}\n          </div>\n\n          {/* 姓名输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <User className=\"w-4 h-4 inline mr-2\" />\n              姓名\n            </label>\n            <input\n              type=\"text\"\n              className={`form-input ${errors.name ? 'error' : ''}`}\n              placeholder=\"请输入您的姓名\"\n              {...register('name', {\n                required: '姓名不能为空',\n                minLength: {\n                  value: 2,\n                  message: '姓名至少需要2个字符'\n                },\n                maxLength: {\n                  value: 50,\n                  message: '姓名不能超过50个字符'\n                }\n              })}\n            />\n            {errors.name && (\n              <p className=\"form-error\">{errors.name.message}</p>\n            )}\n          </div>\n\n          {/* 邮箱输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <Mail className=\"w-4 h-4 inline mr-2\" />\n              邮箱地址\n            </label>\n            <input\n              type=\"email\"\n              className={`form-input ${errors.email ? 'error' : ''}`}\n              placeholder=\"请输入邮箱地址\"\n              {...register('email', {\n                required: '邮箱地址不能为空',\n                pattern: {\n                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                  message: '请输入有效的邮箱地址'\n                }\n              })}\n            />\n            {errors.email && (\n              <p className=\"form-error\">{errors.email.message}</p>\n            )}\n          </div>\n\n          {/* 手机号输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <Phone className=\"w-4 h-4 inline mr-2\" />\n              手机号码\n            </label>\n            <input\n              type=\"tel\"\n              className={`form-input ${errors.phone ? 'error' : ''}`}\n              placeholder=\"请输入手机号码\"\n              {...register('phone', {\n                required: '手机号码不能为空',\n                pattern: {\n                  value: /^1[3-9]\\d{9}$/,\n                  message: '请输入有效的手机号码'\n                }\n              })}\n            />\n            {errors.phone && (\n              <p className=\"form-error\">{errors.phone.message}</p>\n            )}\n          </div>\n\n          {/* 性别和年龄 */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">性别</label>\n              <select\n                className={`form-input ${errors.gender ? 'error' : ''}`}\n                {...register('gender', {\n                  required: '请选择性别'\n                })}\n              >\n                <option value=\"\">请选择</option>\n                <option value=\"男\">男</option>\n                <option value=\"女\">女</option>\n                <option value=\"其他\">其他</option>\n              </select>\n              {errors.gender && (\n                <p className=\"form-error\">{errors.gender.message}</p>\n              )}\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">年龄</label>\n              <input\n                type=\"number\"\n                className={`form-input ${errors.age ? 'error' : ''}`}\n                placeholder=\"年龄\"\n                {...register('age', {\n                  required: '年龄不能为空',\n                  min: {\n                    value: 1,\n                    message: '年龄必须大于0'\n                  },\n                  max: {\n                    value: 150,\n                    message: '年龄不能超过150'\n                  }\n                })}\n              />\n              {errors.age && (\n                <p className=\"form-error\">{errors.age.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* 地址输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <MapPin className=\"w-4 h-4 inline mr-2\" />\n              地址 <span className=\"text-gray-400\">(可选)</span>\n            </label>\n            <input\n              type=\"text\"\n              className=\"form-input\"\n              placeholder=\"请输入您的地址\"\n              {...register('address')}\n            />\n          </div>\n\n          {/* 密码输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <Lock className=\"w-4 h-4 inline mr-2\" />\n              密码\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                className={`form-input pr-12 ${errors.password ? 'error' : ''}`}\n                placeholder=\"请输入密码\"\n                {...register('password', {\n                  required: '密码不能为空',\n                  minLength: {\n                    value: 6,\n                    message: '密码至少需要6个字符'\n                  }\n                })}\n              />\n              <button\n                type=\"button\"\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? (\n                  <EyeOff className=\"w-5 h-5\" />\n                ) : (\n                  <Eye className=\"w-5 h-5\" />\n                )}\n              </button>\n            </div>\n            {errors.password && (\n              <p className=\"form-error\">{errors.password.message}</p>\n            )}\n          </div>\n\n          {/* 确认密码输入 */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              <Lock className=\"w-4 h-4 inline mr-2\" />\n              确认密码\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showConfirmPassword ? 'text' : 'password'}\n                className={`form-input pr-12 ${errors.confirmPassword ? 'error' : ''}`}\n                placeholder=\"请再次输入密码\"\n                {...register('confirmPassword', {\n                  required: '请确认密码',\n                  validate: value =>\n                    value === password || '两次输入的密码不一致'\n                })}\n              />\n              <button\n                type=\"button\"\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n              >\n                {showConfirmPassword ? (\n                  <EyeOff className=\"w-5 h-5\" />\n                ) : (\n                  <Eye className=\"w-5 h-5\" />\n                )}\n              </button>\n            </div>\n            {errors.confirmPassword && (\n              <p className=\"form-error\">{errors.confirmPassword.message}</p>\n            )}\n          </div>\n\n          {/* 错误信息 */}\n          {errors.root && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n              <p className=\"text-red-600 text-sm\">{errors.root.message}</p>\n            </div>\n          )}\n\n          {/* 注册按钮 */}\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"btn btn-primary w-full\"\n          >\n            {loading ? (\n              <>\n                <div className=\"loading\"></div>\n                注册中...\n              </>\n            ) : (\n              <>\n                <UserPlus className=\"w-4 h-4\" />\n                注册\n              </>\n            )}\n          </button>\n        </form>\n\n        {/* 切换到登录 */}\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-gray-600\">\n            已有账户？\n            <button\n              type=\"button\"\n              onClick={onSwitchToLogin}\n              className=\"text-blue-600 hover:text-blue-800 font-medium ml-1\"\n            >\n              立即登录\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,cAAc;AACrF,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEsB,QAAQ,EAAEC,YAAY;IAAEC;EAAQ,CAAC,GAAGd,OAAO,CAAC,CAAC;EAErD,MAAM;IACJY,QAAQ;IACRG,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,KAAK;IACLC;EACF,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAEb,MAAM6B,QAAQ,GAAGF,KAAK,CAAC,UAAU,CAAC;EAElC,MAAMG,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF;MACA,MAAM;QAAEC,eAAe;QAAE,GAAGC;MAAS,CAAC,GAAGF,IAAI;MAE7C,MAAMG,MAAM,GAAG,MAAMZ,YAAY,CAACW,QAAQ,CAAC;MAC3C,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClB;QACApB,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLa,QAAQ,CAAC,MAAM,EAAE;UACfQ,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI;QAC7B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,QAAQ,CAAC,MAAM,EAAE;QACfQ,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACE1B,OAAA;IAAK4B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC7B,OAAA;MAAK4B,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB7B,OAAA;QAAK4B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7B,OAAA;UAAK4B,SAAS,EAAC,kHAAkH;UAAAC,QAAA,eAC/H7B,OAAA,CAACH,QAAQ;YAAC+B,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNjC,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAENjC,OAAA;QAAMmB,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;QAACS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAE3D7B,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACR,IAAI;cAACoC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEyB,IAAI,EAAC,MAAM;YACXG,SAAS,EAAE,cAAcb,MAAM,CAACmB,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC1DC,WAAW,EAAC,sCAAQ;YAAA,GAChBzB,QAAQ,CAAC,UAAU,EAAE;cACvB0B,QAAQ,EAAE,SAAS;cACnBC,SAAS,EAAE;gBACTC,KAAK,EAAE,CAAC;gBACRZ,OAAO,EAAE;cACX,CAAC;cACDa,SAAS,EAAE;gBACTD,KAAK,EAAE,EAAE;gBACTZ,OAAO,EAAE;cACX,CAAC;cACDc,OAAO,EAAE;gBACPF,KAAK,EAAE,iBAAiB;gBACxBZ,OAAO,EAAE;cACX;YACF,CAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDlB,MAAM,CAACmB,QAAQ,iBACdlC,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAACmB,QAAQ,CAACR;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACR,IAAI;cAACoC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEyB,IAAI,EAAC,MAAM;YACXG,SAAS,EAAE,cAAcb,MAAM,CAAC0B,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;YACtDN,WAAW,EAAC,4CAAS;YAAA,GACjBzB,QAAQ,CAAC,MAAM,EAAE;cACnB0B,QAAQ,EAAE,QAAQ;cAClBC,SAAS,EAAE;gBACTC,KAAK,EAAE,CAAC;gBACRZ,OAAO,EAAE;cACX,CAAC;cACDa,SAAS,EAAE;gBACTD,KAAK,EAAE,EAAE;gBACTZ,OAAO,EAAE;cACX;YACF,CAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDlB,MAAM,CAAC0B,IAAI,iBACVzC,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAAC0B,IAAI,CAACf;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACP,IAAI;cAACmC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEyB,IAAI,EAAC,OAAO;YACZG,SAAS,EAAE,cAAcb,MAAM,CAAC2B,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YACvDP,WAAW,EAAC,4CAAS;YAAA,GACjBzB,QAAQ,CAAC,OAAO,EAAE;cACpB0B,QAAQ,EAAE,UAAU;cACpBI,OAAO,EAAE;gBACPF,KAAK,EAAE,0CAA0C;gBACjDZ,OAAO,EAAE;cACX;YACF,CAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDlB,MAAM,CAAC2B,KAAK,iBACX1C,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAAC2B,KAAK,CAAChB;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACpD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACL,KAAK;cAACiC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEyB,IAAI,EAAC,KAAK;YACVG,SAAS,EAAE,cAAcb,MAAM,CAAC4B,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YACvDR,WAAW,EAAC,4CAAS;YAAA,GACjBzB,QAAQ,CAAC,OAAO,EAAE;cACpB0B,QAAQ,EAAE,UAAU;cACpBI,OAAO,EAAE;gBACPF,KAAK,EAAE,eAAe;gBACtBZ,OAAO,EAAE;cACX;YACF,CAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDlB,MAAM,CAAC4B,KAAK,iBACX3C,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAAC4B,KAAK,CAACjB;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACpD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA;YAAK4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7B,OAAA;cAAO4B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCjC,OAAA;cACE4B,SAAS,EAAE,cAAcb,MAAM,CAAC6B,MAAM,GAAG,OAAO,GAAG,EAAE,EAAG;cAAA,GACpDlC,QAAQ,CAAC,QAAQ,EAAE;gBACrB0B,QAAQ,EAAE;cACZ,CAAC,CAAC;cAAAP,QAAA,gBAEF7B,OAAA;gBAAQsC,KAAK,EAAC,EAAE;gBAAAT,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7BjC,OAAA;gBAAQsC,KAAK,EAAC,QAAG;gBAAAT,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5BjC,OAAA;gBAAQsC,KAAK,EAAC,QAAG;gBAAAT,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5BjC,OAAA;gBAAQsC,KAAK,EAAC,cAAI;gBAAAT,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EACRlB,MAAM,CAAC6B,MAAM,iBACZ5C,OAAA;cAAG4B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEd,MAAM,CAAC6B,MAAM,CAAClB;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjC,OAAA;YAAK4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7B,OAAA;cAAO4B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCjC,OAAA;cACEyB,IAAI,EAAC,QAAQ;cACbG,SAAS,EAAE,cAAcb,MAAM,CAAC8B,GAAG,GAAG,OAAO,GAAG,EAAE,EAAG;cACrDV,WAAW,EAAC,cAAI;cAAA,GACZzB,QAAQ,CAAC,KAAK,EAAE;gBAClB0B,QAAQ,EAAE,QAAQ;gBAClBU,GAAG,EAAE;kBACHR,KAAK,EAAE,CAAC;kBACRZ,OAAO,EAAE;gBACX,CAAC;gBACDqB,GAAG,EAAE;kBACHT,KAAK,EAAE,GAAG;kBACVZ,OAAO,EAAE;gBACX;cACF,CAAC;YAAC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACDlB,MAAM,CAAC8B,GAAG,iBACT7C,OAAA;cAAG4B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEd,MAAM,CAAC8B,GAAG,CAACnB;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACJ,MAAM;cAACgC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBACvC,eAAAjC,OAAA;cAAM4B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACRjC,OAAA;YACEyB,IAAI,EAAC,MAAM;YACXG,SAAS,EAAC,YAAY;YACtBO,WAAW,EAAC,4CAAS;YAAA,GACjBzB,QAAQ,CAAC,SAAS;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACN,IAAI;cAACkC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YAAK4B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7B,OAAA;cACEyB,IAAI,EAAEnB,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCsB,SAAS,EAAE,oBAAoBb,MAAM,CAACG,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAChEiB,WAAW,EAAC,gCAAO;cAAA,GACfzB,QAAQ,CAAC,UAAU,EAAE;gBACvB0B,QAAQ,EAAE,QAAQ;gBAClBC,SAAS,EAAE;kBACTC,KAAK,EAAE,CAAC;kBACRZ,OAAO,EAAE;gBACX;cACF,CAAC;YAAC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFjC,OAAA;cACEyB,IAAI,EAAC,QAAQ;cACbG,SAAS,EAAC,uFAAuF;cACjGoB,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAuB,QAAA,EAE7CvB,YAAY,gBACXN,OAAA,CAACT,MAAM;gBAACqC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9BjC,OAAA,CAACV,GAAG;gBAACsC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLlB,MAAM,CAACG,QAAQ,iBACdlB,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAACG,QAAQ,CAACQ;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3B7B,OAAA,CAACN,IAAI;cAACkC,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YAAK4B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7B,OAAA;cACEyB,IAAI,EAAEjB,mBAAmB,GAAG,MAAM,GAAG,UAAW;cAChDoB,SAAS,EAAE,oBAAoBb,MAAM,CAACM,eAAe,GAAG,OAAO,GAAG,EAAE,EAAG;cACvEc,WAAW,EAAC,4CAAS;cAAA,GACjBzB,QAAQ,CAAC,iBAAiB,EAAE;gBAC9B0B,QAAQ,EAAE,OAAO;gBACjBa,QAAQ,EAAEX,KAAK,IACbA,KAAK,KAAKpB,QAAQ,IAAI;cAC1B,CAAC;YAAC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFjC,OAAA;cACEyB,IAAI,EAAC,QAAQ;cACbG,SAAS,EAAC,uFAAuF;cACjGoB,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAAAqB,QAAA,EAE3DrB,mBAAmB,gBAClBR,OAAA,CAACT,MAAM;gBAACqC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9BjC,OAAA,CAACV,GAAG;gBAACsC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLlB,MAAM,CAACM,eAAe,iBACrBrB,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEd,MAAM,CAACM,eAAe,CAACK;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLlB,MAAM,CAACmC,IAAI,iBACVlD,OAAA;UAAK4B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7D7B,OAAA;YAAG4B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEd,MAAM,CAACmC,IAAI,CAACxB;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CACN,eAGDjC,OAAA;UACEyB,IAAI,EAAC,QAAQ;UACb0B,QAAQ,EAAEvC,OAAQ;UAClBgB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAEjCjB,OAAO,gBACNZ,OAAA,CAAAE,SAAA;YAAA2B,QAAA,gBACE7B,OAAA;cAAK4B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAEjC;UAAA,eAAE,CAAC,gBAEHjC,OAAA,CAAAE,SAAA;YAAA2B,QAAA,gBACE7B,OAAA,CAACH,QAAQ;cAAC+B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAElC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGPjC,OAAA;QAAK4B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B7B,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gCAE3B,eAAA7B,OAAA;YACEyB,IAAI,EAAC,QAAQ;YACbuB,OAAO,EAAE5C,eAAgB;YACzBwB,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAC/D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CArUIF,YAAY;EAAA,QAG4BL,OAAO,EAQ/CT,OAAO;AAAA;AAAA+D,EAAA,GAXPjD,YAAY;AAuUlB,eAAeA,YAAY;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}