package service

import (
	"errors"
	"log"
	"time"

	"goHomework/model"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db        *gorm.DB
	jwtSecret []byte
}

// NewAuthService 创建认证服务实例
func NewAuthService(db *gorm.DB, jwtSecret string) *AuthService {
	return &AuthService{
		db:        db,
		jwtSecret: []byte(jwtSecret),
	}
}

// HashPassword 加密密码
func (s *AuthService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
func (s *AuthService) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// GenerateToken 生成JWT token
func (s *AuthService) GenerateToken(userID int, email string) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"email":   email,
		"exp":     time.Now().Add(time.Hour * 24 * 7).Unix(), // 7天过期
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.jwtSecret)
}

// ValidateToken 验证JWT token
func (s *AuthService) ValidateToken(tokenString string) (*model.TokenClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid token signing method")
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	userID, ok := claims["user_id"].(float64)
	if !ok {
		return nil, errors.New("invalid user_id in token")
	}

	email, ok := claims["email"].(string)
	if !ok {
		return nil, errors.New("invalid email in token")
	}

	return &model.TokenClaims{
		UserID: int(userID),
		Email:  email,
	}, nil
}

// Login 用户登录
func (s *AuthService) Login(req model.LoginRequest) (*model.LoginResponse, error) {
	var customer model.Customer

	// 添加调试日志
	log.Printf("🔍 尝试登录用户: %s", req.Username)

	// 查找用户 - 按用户名查询
	result := s.db.Where("username = ? AND status != ?", req.Username, model.UserStatusDeleted).First(&customer)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			log.Printf("❌ 用户不存在: %s", req.Username)
			return nil, errors.New("用户名或密码错误")
		}
		log.Printf("❌ 数据库查询错误: %v", result.Error)
		return nil, result.Error
	}

	log.Printf("✅ 找到用户: %s, ID: %d, Status: %d", customer.Username, customer.UserID, customer.Status)

	// 检查用户状态
	if customer.Status == model.UserStatusInactive {
		return nil, errors.New("账户已被禁用")
	}
	if customer.Status == model.UserStatusDeleted {
		return nil, errors.New("账户不存在")
	}

	// 验证密码
	if err := s.VerifyPassword(customer.Password, req.Password); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 生成token
	token, err := s.GenerateToken(customer.UserID, customer.Email)
	if err != nil {
		return nil, errors.New("生成token失败")
	}

	// 清除密码字段
	customer.Password = ""

	return &model.LoginResponse{
		User:  &customer,
		Token: token,
	}, nil
}

// Register 用户注册
func (s *AuthService) Register(req model.RegisterRequest) (*model.Customer, error) {
	// 检查用户名是否已存在
	var existingCustomer model.Customer
	result := s.db.Where("username = ?", req.Username).First(&existingCustomer)
	if result.Error == nil {
		return nil, errors.New("用户名已被注册")
	}
	if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, result.Error
	}

	// 检查邮箱是否已存在
	result = s.db.Where("email = ?", req.Email).First(&existingCustomer)
	if result.Error == nil {
		return nil, errors.New("邮箱已被注册")
	}
	if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, result.Error
	}

	// 检查手机号是否已存在
	result = s.db.Where("phone = ?", req.Phone).First(&existingCustomer)
	if result.Error == nil {
		return nil, errors.New("手机号已被注册")
	}
	if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, result.Error
	}

	// 加密密码
	hashedPassword, err := s.HashPassword(req.Password)
	if err != nil {
		return nil, errors.New("密码加密失败")
	}

	// 创建用户
	customer := model.Customer{
		Username: req.Username,
		Password: hashedPassword,
		Email:    req.Email,
		Phone:    req.Phone,
		Nickname: req.Nickname,
		Status:   model.UserStatusActive,
		Avatar:   req.Avatar,
		Address:  req.Address,
		Role:     "CUSTOMER",
	}

	result = s.db.Create(&customer)
	if result.Error != nil {
		return nil, result.Error
	}

	// 清除密码字段
	customer.Password = ""

	return &customer, nil
}

// GetUserByID 根据ID获取用户信息
func (s *AuthService) GetUserByID(userID int) (*model.Customer, error) {
	var customer model.Customer
	result := s.db.Where("user_id = ? AND status != ?", userID, model.UserStatusDeleted).First(&customer)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 清除密码字段
	customer.Password = ""

	return &customer, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID int, oldPassword, newPassword string) error {
	var customer model.Customer
	result := s.db.Where("user_id = ? AND status != ?", userID, model.UserStatusDeleted).First(&customer)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return result.Error
	}

	// 验证旧密码
	if err := s.VerifyPassword(customer.Password, oldPassword); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := s.HashPassword(newPassword)
	if err != nil {
		return errors.New("密码加密失败")
	}

	// 更新密码
	result = s.db.Model(&customer).Updates(map[string]interface{}{
		"password":   hashedPassword,
		"updated_at": time.Now(),
	})
	if result.Error != nil {
		return result.Error
	}

	return nil
}
