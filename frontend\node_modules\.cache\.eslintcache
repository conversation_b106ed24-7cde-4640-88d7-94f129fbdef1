[{"D:\\gohomeworkproject\\frontend\\src\\index.js": "1", "D:\\gohomeworkproject\\frontend\\src\\App.js": "2", "D:\\gohomeworkproject\\frontend\\src\\contexts\\AuthContext.js": "3", "D:\\gohomeworkproject\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\gohomeworkproject\\frontend\\src\\components\\ProtectedRoute.js": "5", "D:\\gohomeworkproject\\frontend\\src\\pages\\AuthPage.js": "6", "D:\\gohomeworkproject\\frontend\\src\\services\\api.js": "7", "D:\\gohomeworkproject\\frontend\\src\\components\\LoginForm.js": "8", "D:\\gohomeworkproject\\frontend\\src\\components\\RegisterForm.js": "9"}, {"size": 1005, "mtime": 1756893802514, "results": "10", "hashOfConfig": "11"}, {"size": 2422, "mtime": 1756894027882, "results": "12", "hashOfConfig": "11"}, {"size": 4350, "mtime": 1756905506383, "results": "13", "hashOfConfig": "11"}, {"size": 10840, "mtime": 1756897498455, "results": "14", "hashOfConfig": "11"}, {"size": 859, "mtime": 1756894015093, "results": "15", "hashOfConfig": "11"}, {"size": 1586, "mtime": 1756893970386, "results": "16", "hashOfConfig": "11"}, {"size": 4236, "mtime": 1756905542829, "results": "17", "hashOfConfig": "11"}, {"size": 5923, "mtime": 1756916677327, "results": "18", "hashOfConfig": "11"}, {"size": 11256, "mtime": 1756917027657, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "87asjm", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\gohomeworkproject\\frontend\\src\\index.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\App.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\pages\\Dashboard.js", ["47"], [], "D:\\gohomeworkproject\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\pages\\AuthPage.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\services\\api.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\components\\LoginForm.js", [], [], "D:\\gohomeworkproject\\frontend\\src\\components\\RegisterForm.js", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 21, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 21, "endColumn": 22}, "no-unused-vars", "'statsLoading' is assigned a value but never used.", "Identifier", "unusedVar"]