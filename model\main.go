package model

import (
	"time"
)

// UserStatus 用户状态枚举
const (
	UserStatusActive   = 0 // 正常
	UserStatusInactive = 1 // 禁用
	UserStatusDeleted  = 2 // 已删除（软删除标记）
)

// Customer 用户模型
type Customer struct {
	UserID         int        `json:"user_id" gorm:"primaryKey;autoIncrement;column:user_id"`
	Username       string     `json:"username" binding:"required,min=2,max=50" gorm:"uniqueIndex;size:50;column:username"`
	Password       string     `json:"-" binding:"required,min=6" gorm:"not null;size:100;column:password"` // 密码字段，不在JSON中返回
	Email          string     `json:"email" binding:"email" gorm:"size:100;column:email"`
	Phone          string     `json:"phone" gorm:"size:20;column:phone"`
	RegisterTime   time.Time  `json:"register_time" gorm:"autoCreateTime;column:register_time"`
	LastLoginTime  *time.Time `json:"last_login_time" gorm:"column:last_login_time"`
	Status         int        `json:"status" gorm:"default:0;column:status"` // 0-正常,1-禁用
	Avatar         string     `json:"avatar" gorm:"size:255;column:avatar"`
	Address        string     `json:"address" gorm:"size:255;column:address"`
	Nickname       string     `json:"nickname" gorm:"size:50;column:nickname"`
	Role           string     `json:"role" gorm:"default:CUSTOMER;size:20;column:role"` // CUSTOMER-普通用户,MANUFACTURER-生产商,ADMIN-管理员
	ManufacturerID *int       `json:"manufacturer_id" gorm:"column:manufacturer_id"`
}

// TableName 指定表名为 user
func (Customer) TableName() string {
	return "user"
}

// UpdateCustomerRequest 更新用户请求结构
type UpdateCustomerRequest struct {
	Phone    *string `json:"phone" binding:"omitempty,len=11"`
	Email    *string `json:"email" binding:"omitempty,email"`
	Avatar   *string `json:"avatar"`
	Address  *string `json:"address"`
	Nickname *string `json:"nickname" binding:"omitempty,min=2,max=50"`
}

// CustomerListRequest 用户列表查询请求
type CustomerListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Search   string `form:"search"`
	Status   string `form:"status" binding:"omitempty,oneof=0 1 2"`
	Gender   string `form:"gender" binding:"omitempty,oneof=男 女 其他"`
}

// CustomerListResponse 用户列表响应
type CustomerListResponse struct {
	Data       []Customer `json:"data"`
	Total      int64      `json:"total"`
	Page       int        `json:"page"`
	PageSize   int        `json:"page_size"`
	TotalPages int        `json:"total_pages"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required,min=2,max=50"` // 用户名
	Password string `json:"password" binding:"required,min=6"`
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=2,max=50"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"required,email"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Nickname string `json:"nickname" binding:"required,min=2,max=50"`
	Avatar   string `json:"avatar"`
	Address  string `json:"address"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	User  *Customer `json:"user"`
	Token string    `json:"token"`
}

// TokenClaims JWT Token 声明结构
type TokenClaims struct {
	UserID int    `json:"user_id"`
	Email  string `json:"email"`
}
