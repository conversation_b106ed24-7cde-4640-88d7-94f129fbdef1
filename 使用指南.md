# 用户管理系统 - 完整使用指南

## 🚀 快速开始

### 方法一：一键启动（推荐）

**Windows 用户：**
1. 双击 `start.bat` 文件
2. 等待自动启动完成

**Linux/macOS 用户：**
```bash
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

**启动后端服务：**
```bash
# 在项目根目录
go run main.go
```

**启动前端服务：**
```bash
# 在 frontend 目录
cd frontend
npm install  # 首次运行需要安装依赖
npm start
```

## 📱 访问应用

启动成功后，您可以访问：

- **前端应用**：http://localhost:3000
- **后端 API**：http://localhost:8080
- **健康检查**：http://localhost:8080/health

## 🔐 登录测试

系统提供了两个演示账户供您测试：

### 测试账户1
- **用户名**：张三
- **密码**：123456
- **权限**：基本的用户功能

### 测试账户2
- **用户名**：李四
- **密码**：123456
- **权限**：基本的用户功能

### 测试账户3
- **用户名**：王五
- **密码**：123456
- **权限**：基本的用户功能

## 🎯 功能演示

### 1. 用户注册
1. 在登录页面点击"立即注册"
2. 填写完整的用户信息：
   - 姓名（必填，2-50个字符）
   - 邮箱（必填，有效邮箱格式）
   - 手机号（必填，11位数字）
   - 性别（必填）
   - 年龄（必填，1-150）
   - 地址（可选）
   - 密码（必填，至少6位，包含字母和数字）
3. 点击"注册"按钮
4. 注册成功后自动跳转到登录页面

### 2. 用户登录
1. 输入邮箱和密码
2. 可选择"记住我"保持登录状态
3. 点击"登录"按钮
4. 登录成功后进入用户仪表板

### 3. 用户仪表板
登录后您可以看到：
- **个人信息展示**：姓名、邮箱、手机、年龄、性别、地址
- **账户状态**：当前账户状态和注册时间
- **系统统计**：总用户数、活跃用户数等
- **快速操作**：编辑信息、查看用户等功能

### 4. 安全退出
点击右上角的"退出"按钮安全退出系统

## 🛠️ API 测试

### 使用浏览器测试

**获取用户列表：**
```
http://localhost:8080/api/v1/customers
```

**获取用户统计：**
```
http://localhost:8080/api/v1/customers/stats
```

**健康检查：**
```
http://localhost:8080/health
```

### 使用 PowerShell 测试

**获取用户列表：**
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/v1/customers" -Method GET
```

**创建新用户：**
```powershell
$body = @{
    name = "测试用户"
    gender = "男"
    age = 25
    phone = "13800138888"
    email = "<EMAIL>"
    address = "测试地址"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8080/api/v1/customers" -Method POST -Body $body -ContentType "application/json"
```

## 🎨 界面特性

### 响应式设计
- 支持桌面端、平板和手机
- 自适应不同屏幕尺寸
- 触摸友好的交互设计

### 用户体验
- 流畅的动画效果
- 实时表单验证
- 友好的错误提示
- 加载状态指示

### 现代化 UI
- 渐变背景设计
- 卡片式布局
- 图标化操作
- 统一的视觉风格

## 🔧 自定义配置

### 后端配置

创建 `.env` 文件：
```env
SERVER_PORT=8080
GIN_MODE=debug
RATE_LIMIT_ENABLED=true
RATE_LIMIT_RATE=100
LOG_LEVEL=info
```

### 前端配置

在 `frontend/` 目录创建 `.env` 文件：
```env
REACT_APP_API_URL=http://localhost:8080
REACT_APP_TITLE=用户管理系统
```

## 🐛 常见问题

### Q: 启动时提示端口被占用
**A:** 检查端口 8080 和 3000 是否被其他程序占用，或修改配置文件中的端口设置。

### Q: 前端无法连接后端
**A:** 确保后端服务正在运行，检查 `http://localhost:8080/health` 是否可访问。

### Q: 注册时提示邮箱已存在
**A:** 系统中已有相同邮箱的用户，请使用不同的邮箱地址。

### Q: 登录后页面空白
**A:** 检查浏览器控制台是否有错误信息，确保前后端服务都正常运行。

### Q: 样式显示异常
**A:** 清除浏览器缓存，或尝试硬刷新（Ctrl+F5）。

## 📊 系统监控

### 查看日志
- 后端日志会在控制台实时显示
- 包含请求信息、响应时间、错误信息等

### 性能监控
- 访问 `http://localhost:8080/metrics` 查看系统指标
- 监控请求响应时间和系统状态

## 🔒 安全特性

### 前端安全
- 表单数据验证
- XSS 防护
- 安全的路由保护

### 后端安全
- 请求速率限制
- 数据验证和清理
- 错误信息过滤
- CORS 跨域保护

## 📈 扩展功能

### 可以添加的功能
- 用户头像上传
- 密码重置功能
- 邮箱验证
- 双因素认证
- 用户权限管理
- 操作日志记录

### 数据库集成
当前使用内存存储，可以轻松扩展为：
- MySQL
- PostgreSQL
- MongoDB
- Redis

## 🎓 学习资源

### 技术文档
- [Go 官方文档](https://golang.org/doc/)
- [Gin 框架文档](https://gin-gonic.com/docs/)
- [React 官方文档](https://reactjs.org/docs/)
- [React Router 文档](https://reactrouter.com/)

### 相关教程
- Go Web 开发
- React 前端开发
- RESTful API 设计
- 用户认证系统

## 📞 技术支持

如果您在使用过程中遇到问题，可以：
1. 查看控制台错误信息
2. 检查网络连接
3. 确认服务启动状态
4. 参考本指南的常见问题部分

祝您使用愉快！🎉
