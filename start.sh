#!/bin/bash

echo "========================================"
echo "用户管理系统启动脚本"
echo "========================================"
echo

echo "正在检查环境..."

# 检查 Go 是否安装
if ! command -v go &> /dev/null; then
    echo "[错误] 未找到 Go 环境，请先安装 Go"
    exit 1
fi

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] 未找到 Node.js 环境，请先安装 Node.js"
    exit 1
fi

echo "[成功] 环境检查通过"
echo

# 启动后端服务
echo "正在启动后端服务..."
gnome-terminal --title="后端服务" -- bash -c "echo '后端服务启动中...'; go run main.go; exec bash" 2>/dev/null || \
xterm -title "后端服务" -e "echo '后端服务启动中...'; go run main.go; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "echo \"后端服务启动中...\"; cd \"'$(pwd)'\"; go run main.go"' 2>/dev/null || \
(echo "无法打开新终端窗口，在后台启动后端服务..."; go run main.go &)

# 等待后端服务启动
echo "等待后端服务启动..."
sleep 5

# 检查后端服务是否启动成功
echo "检查后端服务状态..."
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "[成功] 后端服务已启动"
else
    echo "[警告] 后端服务可能未完全启动，请稍等"
fi
echo

# 检查前端依赖
if [ ! -d "frontend/node_modules" ]; then
    echo "正在安装前端依赖..."
    cd frontend
    npm install
    cd ..
    echo "[成功] 前端依赖安装完成"
    echo
fi

# 启动前端服务
echo "正在启动前端服务..."
cd frontend
gnome-terminal --title="前端服务" -- bash -c "echo '前端服务启动中...'; npm start; exec bash" 2>/dev/null || \
xterm -title "前端服务" -e "echo '前端服务启动中...'; npm start; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "echo \"前端服务启动中...\"; cd \"'$(pwd)'\"; npm start"' 2>/dev/null || \
(echo "无法打开新终端窗口，在后台启动前端服务..."; npm start &)
cd ..

echo
echo "========================================"
echo "启动完成！"
echo "========================================"
echo
echo "后端服务: http://localhost:8080"
echo "前端应用: http://localhost:3000"
echo
echo "演示账户:"
echo "用户名: 张三 / 密码: 123456"
echo "用户名: 李四 / 密码: 123456"
echo "用户名: 王五 / 密码: 123456"
echo
echo "按 Ctrl+C 退出"

# 等待用户中断
trap 'echo "正在关闭服务..."; exit 0' INT
while true; do
    sleep 1
done
