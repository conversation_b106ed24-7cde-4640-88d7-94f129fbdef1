@echo off
echo ========================================
echo 用户管理系统启动脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查 Go 是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Go 环境，请先安装 Go
    pause
    exit /b 1
)

:: 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Node.js 环境，请先安装 Node.js
    pause
    exit /b 1
)

echo [成功] 环境检查通过
echo.

:: 启动后端服务
echo 正在启动后端服务...
start "后端服务" cmd /k "echo 后端服务启动中... && go run main.go"

:: 等待后端服务启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 检查后端服务是否启动成功
echo 检查后端服务状态...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8080/health' -TimeoutSec 5 | Out-Null; Write-Host '[成功] 后端服务已启动' } catch { Write-Host '[警告] 后端服务可能未完全启动，请稍等' }"
echo.

:: 检查前端依赖
if not exist "frontend\node_modules" (
    echo 正在安装前端依赖...
    cd frontend
    npm install
    cd ..
    echo [成功] 前端依赖安装完成
    echo.
)

:: 启动前端服务
echo 正在启动前端服务...
cd frontend
start "前端服务" cmd /k "echo 前端服务启动中... && npm start"
cd ..

echo.
echo ========================================
echo 启动完成！
echo ========================================
echo.
echo 后端服务: http://localhost:8080
echo 前端应用: http://localhost:3000
echo.
echo 演示账户:
echo 用户名: 张三 / 密码: 123456
echo 用户名: 李四 / 密码: 123456
echo 用户名: 王五 / 密码: 123456
echo.
echo 按任意键退出启动脚本...
pause >nul
